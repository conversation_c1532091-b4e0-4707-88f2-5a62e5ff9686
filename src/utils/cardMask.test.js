/**
 * Тесты для утилиты маскирования номеров карт
 */

// Копируем функцию для тестирования
function maskCardNumber(cardNumber) {
  if (!cardNumber || typeof cardNumber !== 'string') {
    return cardNumber;
  }

  // Убираем все пробелы, дефисы и другие символы, оставляем только цифры
  const cleanNumber = cardNumber.replace(/\D/g, '');

  // Если номер слишком короткий, возвращаем как есть
  if (cleanNumber.length < 10) {
    return cardNumber;
  }

  // Берем первые 6 цифр, звездочки и последние 4 цифры
  const firstSix = cleanNumber.substring(0, 6);
  const lastFour = cleanNumber.substring(cleanNumber.length - 4);
  const maskedMiddle = '*'.repeat(cleanNumber.length - 10);

  return `${firstSix}${maskedMiddle}${lastFour}`;
}

// Простые тесты для проверки функциональности
console.log('Тестирование функции maskCardNumber:');

// Тест 1: Обычный номер карты 16 цифр
const test1 = '****************';
const result1 = maskCardNumber(test1);
console.log(`Тест 1: ${test1} -> ${result1}`);
console.log(`Ожидается: 418973******1234, получено: ${result1}`);
console.log(`Результат: ${result1 === '418973******1234' ? 'ПРОЙДЕН' : 'ПРОВАЛЕН'}\n`);

// Тест 2: Номер карты с пробелами
const test2 = '4189 7345 6789 1234';
const result2 = maskCardNumber(test2);
console.log(`Тест 2: ${test2} -> ${result2}`);
console.log(`Ожидается: 418973******1234, получено: ${result2}`);
console.log(`Результат: ${result2 === '418973******1234' ? 'ПРОЙДЕН' : 'ПРОВАЛЕН'}\n`);

// Тест 3: Номер карты с дефисами
const test3 = '4189-7345-6789-1234';
const result3 = maskCardNumber(test3);
console.log(`Тест 3: ${test3} -> ${result3}`);
console.log(`Ожидается: 418973******1234, получено: ${result3}`);
console.log(`Результат: ${result3 === '418973******1234' ? 'ПРОЙДЕН' : 'ПРОВАЛЕН'}\n`);

// Тест 4: Короткий номер (должен остаться без изменений)
const test4 = '123456789';
const result4 = maskCardNumber(test4);
console.log(`Тест 4: ${test4} -> ${result4}`);
console.log(`Ожидается: ${test4}, получено: ${result4}`);
console.log(`Результат: ${result4 === test4 ? 'ПРОЙДЕН' : 'ПРОВАЛЕН'}\n`);

// Тест 5: Пустая строка
const test5 = '';
const result5 = maskCardNumber(test5);
console.log(`Тест 5: "${test5}" -> "${result5}"`);
console.log(`Ожидается: "", получено: "${result5}"`);
console.log(`Результат: ${result5 === '' ? 'ПРОЙДЕН' : 'ПРОВАЛЕН'}\n`);

// Тест 6: null
const test6 = null;
const result6 = maskCardNumber(test6);
console.log(`Тест 6: ${test6} -> ${result6}`);
console.log(`Ожидается: null, получено: ${result6}`);
console.log(`Результат: ${result6 === null ? 'ПРОЙДЕН' : 'ПРОВАЛЕН'}\n`);

// Тест 7: Номер карты 15 цифр (American Express)
const test7 = '***************';
const result7 = maskCardNumber(test7);
console.log(`Тест 7: ${test7} -> ${result7}`);
console.log(`Ожидается: 378282*****0005, получено: ${result7}`);
console.log(`Результат: ${result7 === '378282*****0005' ? 'ПРОЙДЕН' : 'ПРОВАЛЕН'}\n`);

console.log('Тестирование завершено!');
