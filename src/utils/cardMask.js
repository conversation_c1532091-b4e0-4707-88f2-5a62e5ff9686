/**
 * Утилита для маскирования номеров карт
 * Формат: 418973******3860 (первые 6 и последние 4 цифры видны)
 */

export function maskCardNumber(cardNumber) {
  if (!cardNumber || typeof cardNumber !== 'string') {
    return cardNumber;
  }
  
  // Убираем все пробелы, дефисы и другие символы, оставляем только цифры
  const cleanNumber = cardNumber.replace(/\D/g, '');
  
  // Если номер слишком короткий, возвращаем как есть
  if (cleanNumber.length < 10) {
    return cardNumber;
  }
  
  // Берем первые 6 цифр, звездочки и последние 4 цифры
  const firstSix = cleanNumber.substring(0, 6);
  const lastFour = cleanNumber.substring(cleanNumber.length - 4);
  const maskedMiddle = '*'.repeat(cleanNumber.length - 10);
  
  return `${firstSix}${maskedMiddle}${lastFour}`;
}

export default {
  maskCardNumber
};
